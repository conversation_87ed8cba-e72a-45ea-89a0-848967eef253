package dev.pigmomo.yhkit2025.ui.screens

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.graphics.lerp
import androidx.core.view.WindowCompat
import android.app.Activity
import kotlin.math.max
import kotlin.math.min
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Call
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.shape.CircleShape
import coil.compose.rememberAsyncImagePainter
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailData
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.viewmodel.OrderDetailViewModel

/**
 * 订单详情屏幕
 * 显示订单的详细信息，包括状态、商品列表、价格明细等
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderDetailScreen(
    viewModel: OrderDetailViewModel,
    orderDetail: OrderDetailData? = null,
    onBackClick: () -> Unit = {}
) {
    // 设置订单详情数据
    LaunchedEffect(orderDetail) {
        orderDetail?.let { viewModel.setOrderDetailData(it) }
    }

    // 观察ViewModel状态
    val orderDetailData by viewModel.orderDetailData.collectAsState()
    val isLoading by viewModel.isLoading
    val toastMessage by viewModel.toastMessage.collectAsState()

    // 滚动状态
    val scrollState = rememberScrollState()
    val density = LocalDensity.current
    val view = LocalView.current

    // 计算滚动进度，用于控制透明度和标题显示
    val scrollProgress = remember {
        derivedStateOf {
            val maxScroll = with(density) { 50.dp.toPx() } // 200dp后完全收起
            min(1f, scrollState.value / maxScroll)
        }
    }

    // 状态栏和标题的透明度
    val topBarAlpha = scrollProgress.value
    val statusAlpha = 1f - scrollProgress.value

    // 动态设置状态栏颜色
    val backgroundColor = Color(0xFFF5F5F5)
    val whiteColor = Color.White
    val statusBarColor = remember {
        derivedStateOf {
            // 当显示标题时（topBarAlpha > 0.5f）使用白色，否则使用背景色
            if (topBarAlpha > 0.5f) whiteColor else backgroundColor
        }
    }

    // 设置状态栏颜色
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = statusBarColor.value.toArgb()
            // 状态栏图标始终为深色（因为背景是浅色）
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = true
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景色
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F5F5))
        )

        // 主要内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(top = 56.dp) // 为顶部栏留出空间
        ) {
            if (orderDetailData != null) {
                // 订单状态区域（带渐变效果）
                OrderStatusSection(
                    viewModel = viewModel,
                    alpha = statusAlpha
                )

                // 送达照片区域（如果有的话）
                DeliveryPhotosSection(viewModel)

                // 商品列表区域（包含店铺信息）
                ProductListWithShopSection(viewModel)

                // 订单金额信息区域
                OrderPriceSection(viewModel)

                // 订单信息区域
                OrderInfoSection(viewModel)

                // 发票区域
                InvoiceSection(viewModel)

                // 为底部按钮留出空间
                Spacer(modifier = Modifier.height(80.dp))
            } else {
                // 显示空状态或加载状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text("暂无订单详情数据")
                }
            }
        }

        // 透明顶部栏
        TopAppBar(
            title = {
                Text(
                    text = if (topBarAlpha > 0.5f) viewModel.getOrderStatusText() else "",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black, // 始终为黑色
                    modifier = Modifier.alpha(max(0.8f, topBarAlpha))
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.Black, // 始终为黑色
                        modifier = Modifier.size(24.dp)
                    )
                }
            },
            actions = {
                // 添加客服按钮
                IconButton(
                    onClick = {
                        // TODO: 实现客服功能
                    }
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.baseline_perm_phone_msg_24),
                        contentDescription = "客服",
                        tint = Color.Black, // 始终为黑色
                        modifier = Modifier.size(24.dp)
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = if (topBarAlpha > 0.5f) Color.White else Color.Transparent,
                titleContentColor = Color.Black,
                navigationIconContentColor = Color.Black,
                actionIconContentColor = Color.Black
            ),
            modifier = Modifier.alpha(max(0.3f, topBarAlpha))
        )

        // 固定底部按钮
        if (orderDetailData != null) {
            FixedBottomButtonsSection(
                viewModel = viewModel,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

/**
 * 订单状态区域
 */
@Composable
fun OrderStatusSection(
    viewModel: OrderDetailViewModel,
    alpha: Float = 1f
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .alpha(alpha),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = viewModel.getOrderStatusText(),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )

                if (viewModel.getShowTimeslots()) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                    contentDescription = "查看详情",
                        tint = Color.Gray,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }

            Text(
                text = viewModel.getOrderStatusTitle(),
                fontSize = 14.sp,
                color = Color(0xFF666666),
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

/**
 * 送达照片区域
 */
@Composable
fun DeliveryPhotosSection(viewModel: OrderDetailViewModel) {
    val deliveryPhotos = viewModel.getDeliveryPhotos()
    val deliveryTitle = viewModel.getDeliveryPhotosTitle()

    if (deliveryPhotos.isNotEmpty()) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            painter = painterResource(id = R.drawable.baseline_assistant_photo_24),
                            contentDescription = "送达照片",
                            tint = Color.Red,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = deliveryTitle,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Button(
                        onClick = { viewModel.viewDeliveryPhotos() },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Red,
                            contentColor = Color.White
                        ),
                        modifier = Modifier.height(32.dp),
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        Text(
                            text = "查看图片",
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 商品列表区域（包含店铺信息）
 */
@SuppressLint("DefaultLocale")
@Composable
private fun ProductListWithShopSection(viewModel: OrderDetailViewModel) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column {
            // 店铺信息区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 添加店铺图标
                Icon(
                    painter = painterResource(id = R.drawable.baseline_groups_24),
                    contentDescription = "店铺",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = viewModel.getShopName(),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.weight(1f))
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                    contentDescription = "查看店铺",
                    tint = Color.Gray,
                    modifier = Modifier.size(20.dp)
                )
            }

            Divider(color = Color(0xFFE0E0E0), thickness = 1.dp)

            // 商品列表区域
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                val productList = viewModel.getProductList()
                val displayProducts = productList.take(3) // 只显示前3个商品
                val remainingCount = productList.size - displayProducts.size

                displayProducts.forEachIndexed { index, product ->
                    ProductItem(
                        imageUrl = product.imgurl,
                        title = product.title,
                        subtitle = product.subtitle,
                        price = String.format("%.2f", (product.price?.value ?: 0) / 100.0),
                        originalPrice = if ((product.price?.lineprice ?: 0) > (product.price?.value ?: 0)) {
                            String.format("%.2f", (product.price?.lineprice ?: 0) / 100.0)
                        } else null,
                        quantity = "${product.calnum}${product.unit}",
                        actualPrice = String.format("%.2f", product.actualPaidPrice / 100.0),
                        tag = product.titletag?.text,
                        orderRemark = product.orderremark
                    )

                    if (index < displayProducts.size - 1) {
                        Divider(modifier = Modifier.padding(vertical = 8.dp))
                    }
                }

                if (remainingCount > 0) {
                    Divider(modifier = Modifier.padding(vertical = 8.dp))

                    // 显示剩余商品数量
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { },
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "还有${remainingCount}种商品",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                            contentDescription = "查看更多",
                            tint = Color.Gray
                        )
                    }
                }
            }
        }
    }
}

/**
 * 商品项组件
 */
@Composable
private fun ProductItem(
    imageUrl: String,
    title: String,
    subtitle: String = "",
    price: String,
    originalPrice: String? = null,
    quantity: String,
    actualPrice: String,
    tag: String? = null,
    orderRemark: String? = null
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 商品图片
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(Color.LightGray)
        ) {
            if (imageUrl.isNotEmpty()) {
                Image(
                    painter = rememberAsyncImagePainter(imageUrl),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                // 使用默认图标
                Icon(
                    painter = painterResource(id = R.drawable.baseline_egg_alt_24),
                    contentDescription = null,
                    modifier = Modifier
                        .size(30.dp)
                        .align(Alignment.Center)
                )
            }
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            // 商品标签和标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 标签（如秒杀）
                tag?.let {
                    Box(
                        modifier = Modifier
                            .background(
                                Color.Red,
                                shape = RoundedCornerShape(2.dp)
                            )
                            .padding(horizontal = 4.dp, vertical = 1.dp)
                    ) {
                        Text(
                            text = it,
                            fontSize = 10.sp,
                            color = Color.White
                        )
                    }
                    Spacer(modifier = Modifier.width(4.dp))
                }

                // 商品标题
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 商品数量和价格
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = "单价: ¥$price",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                        // 原价（划线价）
                        originalPrice?.let {
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "¥$it",
                                fontSize = 10.sp,
                                color = Color.Gray,
                                textDecoration = androidx.compose.ui.text.style.TextDecoration.LineThrough
                            )
                        }
                    }
                    Text(
                        text = "数量: $quantity",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
            }

            // 订单备注（如生产日期）
            orderRemark?.let {
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = it,
                    fontSize = 10.sp,
                    color = Color.Gray
                )
            }
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 实付价格
        Column(horizontalAlignment = Alignment.End) {
            Text(
                text = "实付: ¥$actualPrice",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            originalPrice?.let {
                Text(
                    text = "¥$it",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    textDecoration = androidx.compose.ui.text.style.TextDecoration.LineThrough
                )
            }
        }
    }
}

/**
 * 订单金额信息区域
 */
@Composable
private fun OrderPriceSection(viewModel: OrderDetailViewModel) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            val priceDetailList = viewModel.getPriceDetailList()

            // 显示价格详情列表
            priceDetailList.forEach { priceDetail ->
                val valueColor = if (priceDetail.highlight == 1) Color.Red else Color.Black
                val strikethrough = if (priceDetail.linyAmount != null) priceDetail.linyAmount else ""
                PriceItem(
                    label = priceDetail.title,
                    value = priceDetail.amount,
                    valueColor = valueColor,
                    strikethrough = strikethrough
                )
            }

            Divider(modifier = Modifier.padding(vertical = 8.dp))

            // 实付金额
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = "已优惠",
                        fontSize = 14.sp
                    )
                    Text(
                        text = "¥${viewModel.getTotalDiscount()}",
                        fontSize = 14.sp,
                        color = Color.Red,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }

                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = "实付",
                        fontSize = 14.sp
                    )
                    Text(
                        text = "¥${viewModel.getTotalPayment()}",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }
            }
        }
    }
}

/**
 * 价格项组件
 */
@Composable
private fun PriceItem(
    label: String,
    value: String,
    valueColor: Color = Color.Black,
    strikethrough: String = ""
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = Color(0xFF666666)
        )

        Row(verticalAlignment = Alignment.CenterVertically) {
            if (strikethrough.isNotEmpty()) {
                Text(
                    text = strikethrough,
                    fontSize = 14.sp,
                    color = Color(0xFF999999),
                    textDecoration = androidx.compose.ui.text.style.TextDecoration.LineThrough,
                    modifier = Modifier.padding(end = 4.dp)
                )
            }
            Text(
                text = value,
                fontSize = 14.sp,
                color = valueColor,
                fontWeight = if (valueColor == Color.Red) FontWeight.Medium else FontWeight.Normal
            )
        }
    }
}

/**
 * 订单信息区域
 */
@Composable
private fun OrderInfoSection(viewModel: OrderDetailViewModel) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 预约时间
            InfoItem("预约时间", viewModel.getExpectedTime())

            // 收货地址
            InfoItem("收货地址", viewModel.getDeliveryAddress())

            // 收货人
            InfoItem("收货人", viewModel.getReceiverInfo())

            // 配送员信息
            val carrierInfo = viewModel.getCarrierInfo()
            if (carrierInfo.isNotEmpty()) {
                InfoItem("配送员", carrierInfo)
            }

            // 订单备注
            val orderComment = viewModel.getOrderComment()
            if (orderComment.isNotEmpty()) {
                InfoItem("订单备注", orderComment)
            }

            // 订单编号
            InfoItem("订单编号", viewModel.getOrderId())

            // 下单时间
            InfoItem("下单时间", viewModel.getOrderTime())

            // 支付方式
            InfoItem("支付方式", viewModel.getPaymentMethod())

            // 缺货信息
            val outOfStockMsg = viewModel.getOutOfStockMessage()
            if (outOfStockMsg.isNotEmpty()) {
                InfoItem("特别说明", outOfStockMsg)
            }
        }
    }
}

/**
 * 信息项组件
 */
@Composable
private fun InfoItem(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp)
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = Color(0xFF666666),
            modifier = Modifier.width(80.dp)
        )

        Text(
            text = value,
            fontSize = 14.sp,
            color = Color.Black,
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * 发票区域
 */
@Composable
private fun InvoiceSection(viewModel: OrderDetailViewModel) {
    val invoiceInfo = viewModel.getInvoiceInfo()

    if (invoiceInfo.isNotEmpty()) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            shape = RoundedCornerShape(8.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { viewModel.openInvoice() }
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "发票",
                    fontSize = 14.sp,
                    color = Color.Gray
                )

                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = invoiceInfo,
                        fontSize = 14.sp
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                        contentDescription = "查看发票",
                        tint = Color.Gray
                    )
                }
            }
        }
    }
}

/**
 * 固定底部按钮区域
 */
@Composable
private fun FixedBottomButtonsSection(
    viewModel: OrderDetailViewModel,
    modifier: Modifier = Modifier
) {
    val buttons = viewModel.getStatusButtons()

    if (buttons.isNotEmpty()) {
        // 固定在底部的按钮区域
        Surface(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            color = Color.White,
            shadowElevation = 8.dp,
            shape = RoundedCornerShape(8.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 只显示前2个重要按钮，符合截图样式
                buttons.take(2).forEachIndexed { index, button ->
                    Button(
                        onClick = {
                            when (button.actiontype) {
                                2 -> viewModel.showToast("评价功能待实现") // 评价
                                3 -> viewModel.reorder() // 再来一单
                                9 -> viewModel.showToast("申请售后功能待实现") // 申请售后
                                16 -> viewModel.deleteOrder() // 删除订单
                                40 -> viewModel.showToast("打赏骑手功能待实现") // 打赏骑手
                                else -> viewModel.showToast("功能待实现")
                            }
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (button.highlight == 1) Color.Red else Color(0xFFF5F5F5),
                            contentColor = if (button.highlight == 1) Color.White else Color.Black
                        ),
                        modifier = Modifier
                            .padding(start = if (index > 0) 12.dp else 0.dp)
                            .height(40.dp),
                        shape = RoundedCornerShape(20.dp)
                    ) {
                        Text(
                            text = button.actionname,
                            fontSize = 14.sp,
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )
                    }
                }
            }
        }
    }
}